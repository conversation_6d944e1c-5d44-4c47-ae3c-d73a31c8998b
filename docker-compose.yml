services:
  ubuntu-vscode-dev:
    image: accetto/debian-vnc-xfce-vscode-g3:latest
    container_name: ubuntu-vscode-dev
    hostname: ubuntu-dev
    
    # 端口映射
    ports:
      - "6901:6901"    # VNC Web界面
      - "5901:5901"    # VNC客户端连接
      - "3000:3000"    # 开发服务器端口
      - "8080:8080"    # 备用端口
      - "9000:9000"    # 备用端口
    
    # 环境变量
    environment:
      - VNC_PW=vncpassword           # VNC密码
      - VNC_RESOLUTION=1920x1080     # 分辨率
      - VNC_COL_DEPTH=24             # 颜色深度
      - VNC_VIEW_ONLY=false          # 允许交互
      # 代理配置已禁用 - 如需使用请确保代理服务器可用
      # - http_proxy=http://************:7890      # HTTP代理
      # - https_proxy=http://************:7890     # HTTPS代理
      # - no_proxy=localhost,127.0.0.1,::1,***********/16,10.0.0.0/8,**********/12
    
    # 卷挂载
    volumes:
      - ./workspace:/home/<USER>/workspace     # 工作目录
      - ./vscode-config:/home/<USER>/.config/Code  # VSCode配置
      - ./projects:/home/<USER>/projects       # 项目目录
      - /var/run/docker.sock:/var/run/docker.sock:ro  # Docker socket（只读）
    
    # 网络配置
    networks:
      - dev-network
    
    # 重启策略
    restart: unless-stopped
    
    # 共享内存大小（提升性能）
    shm_size: 512m
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6901"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# 网络配置
networks:
  dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

# 卷配置
volumes:
  workspace:
    driver: local
  vscode-config:
    driver: local
  projects:
    driver: local
