# Ubuntu VSCode开发环境

基于 `accetto/debian-vnc-xfce-vscode-g3` 的完整开发环境，预装Visual Studio Code，支持VNC远程桌面访问。

##  快速启动

### 方法一：使用启动脚本（推荐）
```bash
./start.sh
```

### 方法二：使用docker-compose
```bash
# 启动环境
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止环境
docker-compose down
```

##  访问方式

- **Web VNC界面**: http://localhost:6901
- **VNC客户端**: localhost:5901 (密码: vncpassword)
- **分辨率**: 1920x1080

## 📁 目录结构

```
ubuntu-vscode-xfte/
├── docker-compose.yml     # Docker Compose配置
├── start.sh              # 启动脚本
├── README.md             # 说明文档
├── workspace/            # 工作目录（映射到容器内）
├── projects/             # 项目目录（映射到容器内）
└── vscode-config/        # VSCode配置（映射到容器内）
```

## 🔧 功能特性

### 预装软件
- **Ubuntu/Debian** 系统
- **XFCE** 桌面环境
- **Visual Studio Code** 代码编辑器
- **VNC/noVNC** 远程桌面支持

### 网络配置
- **代理服务器**: ************:7890
- **端口映射**:
  - 6901: VNC Web界面
  - 5901: VNC客户端
  - 3000: 开发服务器
  - 8080: 备用端口
  - 9000: 备用端口

### 数据持久化
- `./workspace` → `/home/<USER>/workspace`
- `./projects` → `/home/<USER>/projects`
- `./vscode-config` → `/home/<USER>/.config/Code`

##  常用命令

```bash
# 启动环境
docker-compose up -d

# 停止环境
docker-compose down

# 重启环境
docker-compose restart

# 查看容器状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 进入容器
docker-compose exec ubuntu-vscode-dev bash

# 更新镜像
docker-compose pull
docker-compose up -d
```

##  自定义配置

### 修改VNC密码
在 `docker-compose.yml` 中修改 `VNC_PW` 环境变量：
```yaml
environment:
  - VNC_PW=your_new_password
```

### 修改分辨率
在 `docker-compose.yml` 中修改 `VNC_RESOLUTION` 环境变量：
```yaml
environment:
  - VNC_RESOLUTION=1920x1080
```

### 修改代理设置
在 `docker-compose.yml` 中修改代理环境变量：
```yaml
environment:
  - http_proxy=http://your_proxy:port
  - https_proxy=http://your_proxy:port
```

## 故障排除

### 容器无法启动
```bash
# 查看详细日志
docker-compose logs ubuntu-vscode-dev

# 检查端口占用
netstat -an | grep 6901
```

### VNC连接失败
```bash
# 检查容器状态
docker-compose ps

# 测试VNC端口
curl -I http://localhost:6901
```

### 代理连接问题
```bash
# 进入容器测试代理
docker-compose exec ubuntu-vscode-dev bash
curl -I --proxy http://************:7890 http://www.google.com
```

##  环境变量说明

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| VNC_PW | vncpassword | VNC连接密码 |
| VNC_RESOLUTION | 1920x1080 | 桌面分辨率 |
| VNC_COL_DEPTH | 24 | 颜色深度 |
| VNC_VIEW_ONLY | false | 是否只读模式 |
| http_proxy | http://************:7890 | HTTP代理 |
| https_proxy | http://************:7890 | HTTPS代理 |

##  使用建议

1. **项目开发**: 将代码放在 `projects` 目录下
2. **临时文件**: 使用 `workspace` 目录
3. **VSCode配置**: 会自动保存在 `vscode-config` 目录
4. **网络访问**: 所有网络请求会通过配置的代理服务器
5. **数据备份**: 定期备份 `projects` 和 `vscode-config` 目录

##  更新升级

```bash
# 停止当前环境
docker-compose down

# 拉取最新镜像
docker-compose pull

# 重新启动
docker-compose up -d
```
