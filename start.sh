#!/bin/bash

# Ubuntu VSCode开发环境启动脚本

echo " 启动Ubuntu VSCode开发环境..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo " Docker未运行，请先启动Docker"
    exit 1
fi

# 停止并删除现有的测试容器（如果存在）
echo " 清理现有容器..."
docker stop ubuntu-vscode-test 2>/dev/null || true
docker rm ubuntu-vscode-test 2>/dev/null || true

# 启动开发环境
echo "🐳 启动开发环境容器..."
docker-compose up -d

# 等待容器启动
echo " 等待容器启动..."
sleep 10

# 检查容器状态
if docker-compose ps | grep -q "Up"; then
    echo "✅ 开发环境启动成功！"
    echo ""
    echo "🌐 访问方式："
    echo "   Web VNC: http://localhost:6901"
    echo "   VNC客户端: localhost:5901 (密码: vncpassword)"
    echo ""
    echo "📁 目录映射："
    echo "   ./workspace -> /home/<USER>/workspace"
    echo "   ./projects -> /home/<USER>/projects"
    echo "   ./vscode-config -> /home/<USER>/.config/Code"
    echo ""
    echo "🔧 代理配置："
    echo "   HTTP/HTTPS代理: ************:7890"
    echo ""
    echo "📝 常用命令："
    echo "   查看日志: docker-compose logs -f"
    echo "   进入容器: docker-compose exec ubuntu-vscode-dev bash"
    echo "   停止环境: docker-compose down"
    echo ""
    echo "🎉 开发环境已就绪！请访问 http://localhost:6901"
else
    echo "❌ 容器启动失败，请查看日志："
    docker-compose logs
fi
